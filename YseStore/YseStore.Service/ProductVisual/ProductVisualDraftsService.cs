using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Cache;
using YseStore.IService.ProductVisual;
using YseStore.IService.Visual;

namespace YseStore.Service.ProductVisual
{
    /// <summary>
    /// 店铺装修草稿
    /// </summary>
    public class ProductVisualDraftsService : BaseServices<product_visual_drafts>, IProductVisualDraftsService
    {
        private readonly ISqlSugarClient db;
        private readonly ICaching _caching;
        private readonly ILogger<ProductVisualDraftsService> _logger;

        public ProductVisualDraftsService(ISqlSugarClient db, ICaching caching, ILogger<ProductVisualDraftsService> logger)
        {
            this.db = db;
            _caching = caching;
            _logger = logger;
        }

        #region 通过商品id获取草稿id
        public async Task<int> GetProductDraftsIdAsync(int productId)
        {
            try
            {
                int draftsId = 0;
                var model = await QueryByClauseAsync(it=>it.ProductId==productId);
                if (model==null)
                {

                    draftsId = await AddWithIntId(new product_visual_drafts
                    {
                        AccTime = (int)DateTimeOffset.Now.ToUnixTimeSeconds(),
                        Config = "{}",
                        Name = "",
                        ProductId = productId,
                        Visible = true,
                        Themes = "default"
                    });
                     await db.Insertable<product_visual_pages>(new product_visual_pages
                    {
                        AssociationId=0,
                        DraftsId = draftsId,
                        Pages= "productdesc",
                        Plugins = "[]",
                         Config = "{}",
                         TemplateId = 0,
                         IsDeal = false
                    }).ExecuteReturnIdentityAsync();
                }
                return draftsId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetProductDraftsIdAsync失败");
                return 0;
            }
        }
        #endregion
    }
}

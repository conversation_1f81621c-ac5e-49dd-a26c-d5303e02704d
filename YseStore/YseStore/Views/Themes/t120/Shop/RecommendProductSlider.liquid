{% comment %} t120主题推荐产品滑块 {% endcomment %}
{% if RecommendProducts and RecommendProducts.size > 0 %}
    {% for recommendGroup in RecommendProducts %}
        {% if recommendGroup.IsVisible and recommendGroup.Products.size > 0 %}
            <div class="recommend-products-section" data-recommend-id="{{ recommendGroup.RId }}">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <div class="section-header text-center mb-4">
                                {% if recommendGroup.Title and recommendGroup.Title != "" %}
                                    <h2 class="section-title">{{ recommendGroup.Title }}</h2>
                                {% else %}
                                    <h2 class="section-title">{{ "web.global.related_products" | translate }}</h2>
                                {% endif %}
                                {% if recommendGroup.SubTitle and recommendGroup.SubTitle != "" %}
                                    <p class="section-subtitle">{{ recommendGroup.SubTitle }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row product-grid">
                        {% for product in recommendGroup.Products %}
                            <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-4">
                                <div class="product-card">
                                    <!-- Product Rating Stars -->
                                    {% if product.AvgRating > 0 %}
                                        <div class="rating-stars">
                                            {% assign rating = product.AvgRating | default: 0 %}
                                            {% assign whole_stars = rating | floor %}
                                            {% assign half_star = rating | minus: whole_stars %}
                                            {% assign next_star = whole_stars | plus: 1 %}

                                            {% for i in (1..5) %}
                                                {% if i <= whole_stars %}
                                                    <i class="icon-star filled"></i>
                                                {% elsif half_star >= 0.5 and i == next_star %}
                                                    <i class="icon-star filled"></i>
                                                {% else %}
                                                    <i class="icon-star"></i>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    {% endif %}

                                    <a class="product-thumb" href="/products/{{ product.PageUrl }}">
                                        <img class="primary"
                                             src="{% if product.PicPath and product.PicPath != '' %}{{ product.PicPath }}{% else %}{{ static_path }}/assets/images/shop/products/sv1.jpg{% endif %}"
                                             alt="{{ product.ProductName }}">
                                        <img class="hover"
                                             src="{% if product.PicPath_1 and product.PicPath_1 != '' %}{{ product.PicPath_1 }}{% else %}{{ static_path }}/assets/images/shop/products/sv2.jpg{% endif %}"
                                             alt="{{ product.ProductName }}">

                                        <!-- Product Labels -->
                                        {% if product.Stock <= 0 %}
                                            <div class="product-labels rectangular">
                                                <span class="lbl sold-out">{{ "products.goods.soldout" | translate }}</span>
                                            </div>
                                        {% elsif product.PromotionPriceFormat and product.PromotionPriceFormat != "" and product.PromotionPriceFormat != "0" %}
                                            <div class="product-labels rectangular">
                                                <span class="lbl on-sale">{{ "web.global.sale" | translate }}</span>
                                            </div>
                                        {% endif %}
                                    </a>

                                    <h3 class="product-title">
                                        <a href="/products/{{ product.PageUrl }}">{{ product.ProductName }}</a>
                                    </h3>

                                    <h4 class="product-price">
                                        {% if product.PromotionPriceFormat and product.PromotionPriceFormat != "" and product.PromotionPriceFormat != "0" %}
                                            <del>{{ product.PriceFormat }}</del>{{ product.PromotionPriceFormat }}
                                        {% else %}
                                            {{ product.PriceFormat }}
                                        {% endif %}
                                    </h4>

                                    <!-- Dynamic Attributes Variant -->
                                    {% if product.DynamicAttributes != null and product.DynamicAttributes.size > 0 %}
                                        {% comment %} 遍历动态属性，查找有颜色代码的属性 {% endcomment %}
                                        {% for attribute in product.DynamicAttributes %}
                                            {% assign attributeName = attribute[0] %}
                                            {% assign attributeOptions = attribute[1] %}

                                            {% if attributeOptions != null and attributeOptions.size > 0 %}
                                                {% comment %} 检查是否有颜色代码 {% endcomment %}
                                                {% assign hasColorCode = false %}
                                                {% for option in attributeOptions %}
                                                    {% if option.ColorCode != null and option.ColorCode != "" %}
                                                        {% assign hasColorCode = true %}
                                                        {% break %}
                                                    {% endif %}
                                                {% endfor %}

                                                {% if hasColorCode %}
                                                    <div class="product-variants">
                                                        {% for option in attributeOptions %}
                                                            {% if option.ColorCode != null and option.ColorCode != "" %}
                                                                <span class="variant-color"
                                                                      style="background-color: {{ option.ColorCode }};"
                                                                      title="{{ option.Name }}"></span>
                                                            {% endif %}
                                                        {% endfor %}
                                                    </div>
                                                    {% break %} {% comment %} 只显示第一个颜色属性 {% endcomment %}
                                                {% endif %}
                                            {% endif %}
                                        {% endfor %}
                                    {% endif %}

                                    <div class="product-buttons">
                                        <button class="btn btn-outline-secondary btn-sm btn-wishlist"
                                                data-toggle="tooltip"
                                                title="{{ "products.goods.addToFavorites" | translate }}"
                                                data-product-id="{{ product.ProductId }}"
                                                data-is-favorited="{% if product.IsFavorited %}true{% else %}false{% endif %}">
                                            {% if product.IsFavorited %}
                                                <i class="icon-heart"></i>
                                            {% else %}
                                                <i class="icon-heart-o"></i>
                                            {% endif %}
                                        </button>

                                        {% if product.Stock > 0 %}
                                            {% if product.HasOptions %}
                                                <button class="btn btn-outline-primary btn-sm"
                                                        data-product-id="{{ product.ProductId }}"
                                                        onclick="window.location.href='/products/{{ product.PageUrl }}'">
                                                    {{ "web.global.select_options" | translate }}
                                                </button>
                                            {% else %}
                                                <button class="btn btn-outline-primary btn-sm btn-addto-cart"
                                                        data-product-id="{{ product.ProductId }}"
                                                        data-toast data-toast-type="success"
                                                        data-toast-position="topRight"
                                                        data-toast-icon="icon-circle-check"
                                                        data-toast-title="Product"
                                                        data-toast-message="successfuly added to cart!">
                                                    {{ "products.goods.addToCart" | translate }}
                                                </button>
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% endif %}
    {% endfor %}
{% endif %}

<style>
    .recommend-products-section {
        margin-top: 60px;
        margin-bottom: 60px;
    }

    .section-header {
        margin-bottom: 40px;
    }

    .section-title {
        font-size: 2rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 10px;
    }

    .section-subtitle {
        color: #666;
        font-size: 1rem;
        margin-bottom: 0;
    }

    .product-variants {
        margin: 10px 0;
        display: flex;
        gap: 5px;
    }

    .variant-color {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2px solid #fff;
        box-shadow: 0 0 0 1px #ddd;
        cursor: pointer;
    }

    .variant-color:hover {
        transform: scale(1.1);
    }
</style>

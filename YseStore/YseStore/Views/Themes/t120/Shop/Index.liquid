<div class="offcanvas-wrapper">
    <!-- Start Page Title -->
    <div class="page-title">
        <div class="container">
            <div class="column">
                <h1>{{ "web.global.shop" | translate }}</h1>
            </div>
            <ul class="breadcrumbs">
                <li><a href="/">{{ "web.global.home"|translate}}</a></li>
                <li class="separator">&nbsp;</li>
                <li>{{ "web.global.shop" | translate }}</li>
            </ul>
        </div>
    </div>
    <!-- End Page Title -->

    <!--Category Top Description-->
    {% if Model.CategoryTopDescription and Model.CategoryTopDescription.Description_en and Model.CategoryTopDescription.Description_en != "" %}
    <div class="container">
        <div class="category-description-top mb-4">
            {{ Model.CategoryTopDescription.Description_en | raw }}
        </div>
    </div>
    {% endif %}
    <!--End Category Top Description-->

    <!-- Start Page Content -->
    <div class="container padding-top-1x padding-bottom-3x">
        <div class="row">
            <!-- Start Categories Content -->
            <div class="col-lg-9 order-lg-2">
                <!-- Start Toolbar -->
                <div class="shop-toolbar mb-30">
                    <div class="column">
                        <div class="shop-sorting">
                            <label for="sorting">{{ "products.lists.sort_by" | translate }}:</label>
                            <select class="form-control" id="sorting" name="SortBy" onchange="changeSorting()">
                                <option value="title-ascending" {% if Model.SortBy=="title-ascending" %}selected{% endif %}>
                                    {{ "products.lists.sort_by" | translate }}
                                </option>
                                <option value="bestseller" {% if Model.SortBy=="bestseller" %}selected{% endif %}>
                                    {{ "products.lists.sort_by_ary_5" | translate }}
                                </option>
                                <option value="alpha-asc" {% if Model.SortBy=="alpha-asc" %}selected{% endif %}>
                                    Alphabetically, A-Z
                                </option>
                                <option value="alpha-desc" {% if Model.SortBy=="alpha-desc" %}selected{% endif %}>
                                    Alphabetically, Z-A
                                </option>
                                <option value="priceasc" {% if Model.SortBy=="priceasc" %}selected{% endif %}>
                                    {{ "products.lists.sort_by_ary_3" | translate }}
                                </option>
                                <option value="pricedesc" {% if Model.SortBy=="pricedesc" %}selected{% endif %}>
                                    {{ "products.lists.sort_by_ary_4" | translate }}
                                </option>
                                <option value="date-newest" {% if Model.SortBy=="date-newest" %}selected{% endif %}>
                                    {{ "products.lists.sort_by_ary_1" | translate }}
                                </option>
                                <option value="date-oldest" {% if Model.SortBy=="date-oldest" %}selected{% endif %}>
                                    {{ "products.lists.sort_by_ary_2" | translate }}
                                </option>
                            </select>
                            {% if Model.TotalItems > 0 %}
                            <span class="text-muted">{{ "web.global.show" | translate }}: </span>
                            <span>{{ Model.ShowingStart }}-{{ Model.ShowingEnd }} of {{ Model.TotalItems }} {{ "web.global.results" | translate }}</span>
                            {% else %}
                            <span class="text-muted">{{ "products.goods.no_products" | translate }}</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="column">
                        <div class="shop-view">
                            <a class="grid-view active" href="/collections{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}">
                                <span></span>
                                <span></span>
                                <span></span>
                            </a>
                            <a class="list-view" href="/collections{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}ShopList">
                                <span></span>
                                <span></span>
                                <span></span>
                            </a>
                        </div>
                    </div>
                </div>
                <!-- End Toolbar -->
                <!-- Start Products Grid -->
                <div class="row product-grid">
                    {% if Model.Products != null and Model.Products.size > 0 %}
                    {% for product in Model.Products %}
                    <div class="col-12 col-sm-12 col-md-4 col-lg-4 mb-3">
                        <div class="product-card">
                            <!-- Product Rating Stars -->
                            {% if product.AvgRating > 0 %}
                            <div class="rating-stars">
                                {% assign rating = product.AvgRating | default: 0 %}
                                {% assign whole_stars = rating | floor %}
                                {% assign half_star = rating | minus: whole_stars %}
                                {% assign next_star = whole_stars | plus: 1 %}

                                {% for i in (1..5) %}
                                {% if i <= whole_stars %}
                                <i class="icon-star filled"></i>
                                {% elsif half_star >= 0.5 and i == next_star %}
                                <i class="icon-star filled"></i>
                                {% else %}
                                <i class="icon-star"></i>
                                {% endif %}
                                {% endfor %}
                            </div>
                            {% endif %}

                            <a class="product-thumb" href="/products/{{ product.PageUrl }}">
                                <img class="primary"
                                     src="{% if product.PicPath and product.PicPath != '' %}{{ product.PicPath }}{% else %}{{ static_path }}/assets/images/shop/products/sv1.jpg{% endif %}"
                                     alt="{{ product.Title }}">
                                <img class="hover"
                                     src="{% if product.PicPath_1 and product.PicPath_1 != '' %}{{ product.PicPath_1 }}{% else %}{{ static_path }}/assets/images/shop/products/sv2.jpg{% endif %}"
                                     alt="{{ product.Title }}">

                                <!-- Product Labels -->
                                {% if product.Stock <= 0 %}
                                <div class="product-labels rectangular">
                                    <span class="lbl sold-out">{{ "products.goods.soldout" | translate }}</span>
                                </div>
                                {% else %}
                                <div class="product-labels rectangular">
                                    <span class="lbl on-sale">{{ "web.global.sale" | translate }}</span>
                                    <span class="lbl pr-label1">{{ "web.global.new" | translate }}</span>
                                </div>
                                {% endif %}
                            </a>

                            <h3 class="product-title">
                                <a href="/products/{{ product.PageUrl }}">{{ product.ProductName }}</a>
                            </h3>

                            <h4 class="product-price" data-price-container
                                data-original-price="{{ product.OriginalPriceFormat }}"
                                data-current-price="{{ product.PriceFormat }}"
                                data-promotion-price="{{ product.PromotionPriceFormat }}">
                                <!-- 价格内容将通过JavaScript动态生成 -->
                            </h4>

                            <!-- Dynamic Attributes Variant -->
                            {% if product.DynamicAttributes != null and product.DynamicAttributes.size > 0 %}
                            {% comment %} 遍历动态属性，查找有颜色代码的属性 {% endcomment %}
                            {% for attribute in product.DynamicAttributes %}
                            {% assign attributeName = attribute[0] %}
                            {% assign attributeOptions = attribute[1] %}

                            {% if attributeOptions != null and attributeOptions.size > 0 %}
                            {% comment %} 检查是否有颜色代码 {% endcomment %}
                            {% assign hasColorCode = false %}
                            {% for option in attributeOptions %}
                            {% if option.ColorCode != null and option.ColorCode != "" %}
                            {% assign hasColorCode = true %}
                            {% break %}
                            {% endif %}
                            {% endfor %}

                            {% if hasColorCode %}
                            <div class="product-variants">
                                {% for option in attributeOptions %}
                                {% if option.ColorCode != null and option.ColorCode != "" %}
                                <span class="variant-color"
                                      style="background-color: {{ option.ColorCode }};"
                                      title="{{ option.Name }}"></span>
                                {% endif %}
                                {% endfor %}
                            </div>
                            {% break %} {% comment %} 只显示第一个颜色属性 {% endcomment %}
                            {% endif %}
                            {% endif %}
                            {% endfor %}
                            {% endif %}

                            <div class="product-buttons">
                                <button class="btn btn-outline-secondary btn-sm btn-wishlist" data-toggle="tooltip" title="Whishlist">
                                    <i class="icon-heart"></i>
                                </button>
                                <button class="btn btn-outline-primary btn-sm" data-toast data-toast-type="success" data-toast-position="topRight" data-toast-icon="icon-circle-check" data-toast-title="Product" data-toast-message="successfuly added to cart!">Add to Cart</button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    {% else %}
                    <div class="col-12">
                        <p class="text-center">{{ "products.goods.no_products" | translate }}.</p>
                    </div>
                    {% endif %}

                </div>
                <!-- End Products Grid -->
                <!-- Start Pagination -->
                {% if Model.TotalPages > 1 %}
                <nav class="pagination">
                    <div class="column">
                        <ul class="pages">
                            {% assign startPage = Model.CurrentPage | minus: 2 %}
                            {% if startPage < 1 %}{% assign startPage = 1 %}{% endif %}

                            {% assign endPage = startPage | plus: 4 %}
                            {% if endPage > Model.TotalPages %}{% assign endPage = Model.TotalPages %}{% endif %}

                            {% if startPage > 1 %}
                            <li>
                                <a href="/collections{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page=1{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}">1</a>
                            </li>
                            {% if startPage > 2 %}
                            <li>...</li>
                            {% endif %}
                            {% endif %}

                            {% for i in (startPage..endPage) %}
                            <li {% if i==Model.CurrentPage %}class="active"{% endif %}>
                                <a href="/collections{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page={{ i }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}">{{ i }}</a>
                            </li>
                            {% endfor %}

                            {% if endPage < Model.TotalPages %}
                            {% assign lastPageMinusOne = Model.TotalPages | minus: 1 %}
                            {% if endPage < lastPageMinusOne %}
                            <li>...</li>
                            {% endif %}
                            <li>
                                <a href="/collections{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page={{ Model.TotalPages }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}">{{ Model.TotalPages }}</a>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                    <div class="column text-right hidden-xs-down">
                        {% if Model.CurrentPage < Model.TotalPages %}
                        <a class="btn btn-outline-secondary btn-sm"
                           href="/collections{% if Model.CategoryUrl %}/{{ Model.CategoryUrl }}{% endif %}?page={{ Model.CurrentPage | plus: 1 }}{% if Model.Keyword %}&keyword={{ Model.Keyword }}{% endif %}{% if Model.SortBy %}&sortBy={{ Model.SortBy }}{% endif %}{% if Model.IncludeSoldOut %}&includeSoldOut=true{% endif %}">
                            {{ "web.global.next" | translate }} <i class="icon-arrow-right"></i>
                        </a>
                        {% endif %}
                    </div>
                </nav>
                {% endif %}
                <!-- End Pagination -->
            </div>
            <!-- End Categories Content -->
            <!-- Start Sidebar -->
            <div class="col-lg-3 order-lg-1 hidden-md-down">
                <aside class="sidebar">
                    <div class="padding-top-2x hidden-lg-up"></div>
                    <!-- Start Categories Widget -->
                    <section class="widget widget-categories">
                        <h3 class="widget-title">Shop Categories</h3>
                        <ul>
                            <li class="has-children expanded">
                                <a href="#">Mobile Phones</a><span>(1277)</span>
                                <ul>
                                    <li>
                                        <a href="#">Apple</a><span>(700)</span>
                                        <ul>
                                            <li><a href="#">iPhone x 64GB</a></li>
                                            <li><a href="#">iPhone 8 128GB</a></li>
                                            <li><a href="#">iPhone 7 Red</a></li>
                                            <li><a href="#">iPhone 6 Plus</a></li>
                                        </ul>
                                    </li>
                                    <li>
                                        <a href="#">Samsung</a><span>(500)</span>
                                        <ul>
                                            <li><a href="#">Samsung Galaxy S8</a></li>
                                            <li><a href="#">Samsung Galaxy S7</a></li>
                                            <li><a href="#">Samsung Galaxy J5</a></li>
                                            <li><a href="#">Samsung Golden A5</a></li>
                                        </ul>
                                    </li>
                                    <li><a href="#">Nokia Mobile</a><span>(37)</span></li>
                                    <li><a href="#">BlackBerry Phones</a><span>(42)</span></li>
                                </ul>
                            </li>
                            <li class="has-children">
                                <a href="#">PC & Laptops</a><span>(800)</span>
                                <ul>
                                    <li>
                                        <a href="#">Apple</a><span>(500)</span>
                                        <ul>
                                            <li><a href="#">MacBook Pro 13.3</a></li>
                                            <li><a href="#">MacBook Pro 15.6</a></li>
                                            <li><a href="#">MacBook Pro 17</a></li>
                                            <li><a href="#">MacBook Pro 19</a></li>
                                        </ul>
                                    </li>
                                    <li>
                                        <a href="#">Lenovo</a><span>(250)</span>
                                        <ul>
                                            <li><a href="#">Lenovo Core i3</a></li>
                                            <li><a href="#">Lenovo Core i5</a></li>
                                            <li><a href="#">Lenovo Core i7</a></li>
                                            <li><a href="#">Lenovo Core 2 Quad</a></li>
                                        </ul>
                                    </li>
                                    <li><a href="#">Asus</a><span>(50)</span></li>
                                </ul>
                            </li>
                            <li class="has-children">
                                <a href="#">Tabs & Tablets</a><span>(420)</span>
                                <ul>
                                    <li><a href="#">iPad 3 1TB</a><span>(180)</span></li>
                                    <li><a href="#">Samsung Tab Pro</a><span>(140)</span></li>
                                    <li><a href="#">Lenovo Tab 3TB</a><span>(100)</span></li>
                                </ul>
                            </li>
                            <li class="has-children">
                                <a href="#">Accessories</a><span>(760)</span>
                                <ul>
                                    <li><a href="#">Televisions</a><span>(160)</span></li>
                                    <li><a href="#">Camcorders</a><span>(100)</span></li>
                                    <li><a href="#">Watches</a><span>(100)</span></li>
                                    <li><a href="#">Jewelry</a><span>(200)</span></li>
                                    <li><a href="#">Other</a><span>(200)</span></li>
                                </ul>
                            </li>
                        </ul>
                    </section>
                    <!-- End Categories Widget -->
                    <!--  Start Price Range Widget -->
                    <section class="widget widget-categories">
                        <h3 class="widget-title">Price Range</h3>
                        <form class="price-range-slider" method="post" data-start-min="250" data-start-max="970" data-min="0" data-max="1500" data-step="1">
                            <div class="ui-range-slider"></div>
                            <footer class="ui-range-slider-footer">
                                <div class="column">
                                    <button class="btn btn-outline-primary btn-sm" type="submit">Filter</button>
                                </div>
                                <div class="column">
                                    <div class="ui-range-values">
                                        <div class="ui-range-value-min">
                                            $<span></span>
                                            <input type="hidden">
                                        </div> -
                                        <div class="ui-range-value-max">
                                            $<span></span>
                                            <input type="hidden">
                                        </div>
                                    </div>
                                </div>
                            </footer>
                        </form>
                    </section>
                    <!--  End Price Range Widget -->
                    <!-- Start Brand Filter Widget -->
                    <section class="widget">
                        <h3 class="widget-title">Filter by Brand</h3>
                        <div class="custom-control custom-checkbox">
                            <input class="custom-control-input" type="checkbox" id="adidas">
                            <label class="custom-control-label" for="adidas">Apple <span class="text-muted">(197)</span></label>
                        </div>
                        <div class="custom-control custom-checkbox">
                            <input class="custom-control-input" type="checkbox" id="bilabong">
                            <label class="custom-control-label" for="bilabong">Samsung <span class="text-muted">(123)</span></label>
                        </div>
                        <div class="custom-control custom-checkbox">
                            <input class="custom-control-input" type="checkbox" id="klein">
                            <label class="custom-control-label" for="klein">Sony <span class="text-muted">(148)</span></label>
                        </div>
                        <div class="custom-control custom-checkbox">
                            <input class="custom-control-input" type="checkbox" id="nike">
                            <label class="custom-control-label" for="nike">Lenovo <span class="text-muted">(164)</span></label>
                        </div>
                        <div class="custom-control custom-checkbox">
                            <input class="custom-control-input" type="checkbox" id="bahama">
                            <label class="custom-control-label" for="bahama">Panasonic <span class="text-muted">(139)</span></label>
                        </div>
                    </section>
                    <!-- End Brand Filter Widget -->
                    <!-- Start Size Filter Widget -->
                    <section class="widget">
                        <h3 class="widget-title">Filter by Size</h3>
                        <div class="custom-control custom-checkbox">
                            <input class="custom-control-input" type="checkbox" id="xl">
                            <label class="custom-control-label" for="xl">64GB <span class="text-muted">(208)</span></label>
                        </div>
                        <div class="custom-control custom-checkbox">
                            <input class="custom-control-input" type="checkbox" id="l">
                            <label class="custom-control-label" for="l">128GB <span class="text-muted">(311)</span></label>
                        </div>
                        <div class="custom-control custom-checkbox">
                            <input class="custom-control-input" type="checkbox" id="m">
                            <label class="custom-control-label" for="m">256GB <span class="text-muted">(485)</span></label>
                        </div>
                        <div class="custom-control custom-checkbox">
                            <input class="custom-control-input" type="checkbox" id="s">
                            <label class="custom-control-label" for="s">512GB <span class="text-muted">(213)</span></label>
                        </div>
                        <div class="custom-control custom-checkbox">
                            <input class="custom-control-input" type="checkbox" id="ss">
                            <label class="custom-control-label" for="s">1TB <span class="text-muted">(213)</span></label>
                        </div>
                    </section>
                    <!-- End Size Filter Widget -->
                </aside>
            </div>
            <!-- End Sidebar -->
        </div>

        <!--Category Bottom Description-->
        {% if Model.CategoryBottomDescription and Model.CategoryBottomDescription.Description_en_bottom and Model.CategoryBottomDescription.Description_en_bottom != "" %}
        <div class="row">
            <div class="col-12">
                <div class="category-description-bottom mt-4">
                    {{ Model.CategoryBottomDescription.Description_en_bottom | raw }}
                </div>
            </div>
        </div>
        {% endif %}
        <!--End Category Bottom Description-->

        <!--Recommend Products-->
        {% if Model.RecommendProducts != null and Model.RecommendProducts.size > 0 %}
        {% assign recommendproductslider = '/Themes/' | append: theme | append: '/Shop/RecommendProductSlider' %}
        {% include recommendproductslider, RecommendProducts: Model.RecommendProducts -%}
        {% endif %}
        <!--End Recommend Products-->
    </div>
    <!-- End Page Content -->
</div>

<script>
// 排序功能
function changeSorting() {
    const sortBy = document.getElementById('sorting').value;
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('sortBy', sortBy);
    currentUrl.searchParams.set('page', '1'); // 重置到第一页
    window.location.href = currentUrl.toString();
}

// 价格显示逻辑
document.addEventListener('DOMContentLoaded', function() {
    const priceContainers = document.querySelectorAll('[data-price-container]');

    priceContainers.forEach(container => {
        const originalPrice = container.getAttribute('data-original-price');
        const currentPrice = container.getAttribute('data-current-price');
        const promotionPrice = container.getAttribute('data-promotion-price');

        let priceHtml = '';

        if (promotionPrice && promotionPrice !== '' && promotionPrice !== '0' && promotionPrice !== '$0.00') {
            // 有促销价格
            priceHtml = `<del>${originalPrice}</del>${promotionPrice}`;
        } else if (originalPrice !== currentPrice && originalPrice && originalPrice !== '' && originalPrice !== '0' && originalPrice !== '$0.00') {
            // 有原价和现价
            priceHtml = `<del>${originalPrice}</del>${currentPrice}`;
        } else {
            // 只有当前价格
            priceHtml = currentPrice;
        }

        container.innerHTML = priceHtml;
    });

    // 心愿单功能
    const wishlistBtns = document.querySelectorAll('.btn-wishlist');
    wishlistBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const productId = this.getAttribute('data-product-id');
            const isFavorited = this.getAttribute('data-is-favorited') === 'true';

            // 这里可以添加AJAX调用来处理心愿单逻辑
            console.log('Wishlist clicked for product:', productId, 'Current state:', isFavorited);

            // 切换图标
            const icon = this.querySelector('i');
            if (isFavorited) {
                icon.className = 'icon-heart-o';
                this.setAttribute('data-is-favorited', 'false');
            } else {
                icon.className = 'icon-heart';
                this.setAttribute('data-is-favorited', 'true');
            }
        });
    });

    // 添加到购物车功能
    const addToCartBtns = document.querySelectorAll('.btn-addto-cart');
    addToCartBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const productId = this.getAttribute('data-product-id');

            // 这里可以添加AJAX调用来处理添加到购物车逻辑
            console.log('Add to cart clicked for product:', productId);
        });
    });
});
</script>
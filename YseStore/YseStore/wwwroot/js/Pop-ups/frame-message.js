/**
 * 现代化消息提示框封装
 * 实现独立、现代风格的消息提示、确认对话框、加载提示等功能
 */
let customize_pop = (function() {
    // 默认配置
    const defaultConfig = {
        // 消息显示时间，单位毫秒
        messageDuration: 2000,
        // 加载提示的文字
        loadingText: '加载中...',
        // 确认对话框标题
        confirmTitle: '确认操作',
        // 弹窗z-index起始值
        zIndex: 9000,
        // 弹窗动画持续时间(ms)
        animationDuration: 300,
        // 主题颜色
        colors: {
            success: '#67C23A',
            error: '#F56C6C',
            warning: '#E6A23C',
            info: '#909399',
            primary: '#409EFF'
        },
        // CSS文件路径
        cssPath: '/css/popup/popup.css'
    };

    // 当前弹窗计数
    let popupCounter = 0;
    // 当前加载实例
    let loadingInstance = null;
    // 记录所有弹窗
    let popupInstances = {};
    // CSS是否已加载标志
    let cssLoaded = false;

    // 加载CSS文件
    const loadCSS = function() {
        if (cssLoaded) return Promise.resolve();
        
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.type = 'text/css';
            link.href = defaultConfig.cssPath;
            link.onload = () => {
                cssLoaded = true;
                resolve();
            };
            link.onerror = () => {
                console.error('无法加载弹窗CSS文件:', defaultConfig.cssPath);
                reject();
            };
            document.head.appendChild(link);
        });
    };

    // 创建SVG图标
    const createIcon = function(type) {
        let svg = '';
        switch(type) {
            case 'success':
                svg = '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M5 13l4 4L19 7" stroke-linecap="round" stroke-linejoin="round"/></svg>';
                break;
            case 'error':
                svg = '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round"/></svg>';
                break;
            case 'warning':
                svg = '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 9v4m0 4h.01M12 2a10 10 0 100 20z" stroke-linecap="round" stroke-linejoin="round"/></svg>';
                break;
            case 'info':
                svg = '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M13 16h-2v-6h2v6zm0-8h-2v-2h2v2zm-1-6C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z" stroke-linecap="round" stroke-linejoin="round"/></svg>';
                break;
            case 'loading':
                svg = '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10" opacity=".3"/><path d="M12 2a10 10 0 0110 10" stroke-linecap="round" stroke-linejoin="round"><animateTransform attributeName="transform" type="rotate" from="0 12 12" to="360 12 12" dur="1s" repeatCount="indefinite"/></path></svg>';
                break;
            case 'close':
                svg = '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round"/></svg>';
                break;
        }
        return `<span class="popup-icon ${type}">${svg}</span>`;
    };

    // 创建消息提示
    const createMessage = function(message, type, duration, callback, options = {}) {
        return loadCSS().then(() => {
            const id = 'popup-message-' + Date.now();
            const messageEl = document.createElement('div');
            messageEl.className = `popup-message ${type}`;
            messageEl.id = id;
            messageEl.style.zIndex = defaultConfig.zIndex + popupCounter++;
            
            // 创建关闭按钮
            const closeButton = `<span class="popup-message-close">${createIcon('close')}</span>`;
            
            // 只有当showIcon不为false时才显示图标
            const showIcon = options.showIcon !== false;
            const iconHtml = showIcon ? `
                <div class="popup-message-icon">
                    ${createIcon(type)}
                </div>
            ` : '';
            
            messageEl.innerHTML = `
                ${iconHtml}
                <div class="popup-message-content">${message}</div>
                ${duration === 0 ? closeButton : ''}
            `;
            
            document.body.appendChild(messageEl);
            
            // 添加关闭按钮事件
            if (duration === 0) {
                const closeBtn = messageEl.querySelector('.popup-message-close');
                if (closeBtn) {
                    closeBtn.addEventListener('click', () => {
                        closeMessage(id);
                    });
                }
            }
            
            // 显示消息
            setTimeout(() => {
                messageEl.classList.add('show');
            }, 10);
            
            // 设置消息自动关闭
            if (duration !== 0) {
                setTimeout(() => {
                    closeMessage(id, callback);
                }, duration || defaultConfig.messageDuration);
            }
            
            return id;
        }).catch(() => {
            // CSS加载失败时的降级处理
            alert(message);
            if (typeof callback === 'function') {
                callback();
            }
            return null;
        });
    };

    // 关闭消息
    const closeMessage = function(id, callback) {
        const messageEl = document.getElementById(id);
        if (!messageEl) return;
        
        messageEl.classList.remove('show');
        setTimeout(() => {
            if (messageEl.parentNode) {
                document.body.removeChild(messageEl);
            }
            if (typeof callback === 'function') {
                callback();
            }
        }, defaultConfig.animationDuration);
    };

    // 创建对话框
    const createDialog = function(options) {
        return loadCSS().then(() => {
            const id = 'popup-dialog-' + Date.now();
            const zIndex = defaultConfig.zIndex + popupCounter++;
            
            const overlay = document.createElement('div');
            overlay.className = 'popup-overlay';
            overlay.id = id;
            overlay.style.zIndex = zIndex;
            
            const container = document.createElement('div');
            container.className = 'popup-container';
            
            if (options.width) {
                container.style.width = options.width;
            }
            if (options.height) {
                container.style.height = options.height;
            }
            
            // 标题栏
            if (options.showHeader !== false) {
                const header = document.createElement('div');
                header.className = 'popup-header';
                
                const title = document.createElement('h3');
                title.className = 'popup-title';
                title.textContent = options.title || '提示';
                
                const closeBtn = document.createElement('button');
                closeBtn.className = 'popup-close';
                closeBtn.innerHTML = createIcon('close');
                closeBtn.onclick = function() {
                    closeDialog(id);
                    if (typeof options.onCancel === 'function') {
                        options.onCancel();
                    }
                };
                
                header.appendChild(title);
                header.appendChild(closeBtn);
                container.appendChild(header);
            }
            
            // 内容区域
            const body = document.createElement('div');
            body.className = 'popup-body';
            
            if (options.type === 'confirm') {
                // 确认对话框模式
                const contentWrapper = document.createElement('div');
                contentWrapper.style.display = 'flex';
                contentWrapper.style.alignItems = 'flex-start';
                
                // 只有当showIcon不为false时才显示图标
                if (options.showIcon !== false) {
                    const icon = document.createElement('div');
                    icon.innerHTML = createIcon(options.iconType || 'warning');
                    icon.style.marginRight = '10px';
                    contentWrapper.appendChild(icon);
                }
                
                const content = document.createElement('div');
                content.innerHTML = options.content;
                
                contentWrapper.appendChild(content);
                body.appendChild(contentWrapper);
            } else if (options.type === 'custom') {
                // 自定义内容模式
                body.className += ' popup-custom';
                if (typeof options.content === 'string') {
                    body.innerHTML = options.content;
                } else if (options.content instanceof Node) {
                    body.appendChild(options.content);
                }
            } else {
                body.innerHTML = options.content;
            }
            
            container.appendChild(body);
            
            // 底部按钮
            if (options.showFooter !== false) {
                const footer = document.createElement('div');
                footer.className = 'popup-footer';
                
                if (options.type === 'confirm') {
                    // 取消按钮
                    const cancelBtn = document.createElement('button');
                    cancelBtn.className = 'popup-btn';
                    cancelBtn.textContent = options.cancelText || '取消';
                    cancelBtn.onclick = function() {
                        closeDialog(id);
                        if (typeof options.onCancel === 'function') {
                            options.onCancel();
                        }
                    };
                    
                    // 确认按钮
                    const confirmBtn = document.createElement('button');
                    confirmBtn.className = 'popup-btn popup-btn-primary';
                    confirmBtn.textContent = options.confirmText || '确认';
                    confirmBtn.onclick = function() {
                        closeDialog(id);
                        if (typeof options.onConfirm === 'function') {
                            options.onConfirm();
                        }
                    };
                    
                    footer.appendChild(cancelBtn);
                    footer.appendChild(confirmBtn);
                } else if (options.buttons && options.buttons.length) {
                    // 自定义按钮
                    options.buttons.forEach(btn => {
                        const button = document.createElement('button');
                        button.className = 'popup-btn';
                        if (btn.primary) {
                            button.className += ' popup-btn-primary';
                        }
                        button.textContent = btn.text || '按钮';
                        button.onclick = function() {
                            if (btn.closeOnClick !== false) {
                                closeDialog(id);
                            }
                            if (typeof btn.onClick === 'function') {
                                btn.onClick();
                            }
                        };
                        footer.appendChild(button);
                    });
                }
                
                container.appendChild(footer);
            }
            
            overlay.appendChild(container);
            document.body.appendChild(overlay);
            
            // 记录实例
            popupInstances[id] = {
                element: overlay,
                options: options
            };
            
            // 显示动画
            setTimeout(() => {
                overlay.classList.add('popup-enter');
            }, 10);
            
            return id;
        }).catch(() => {
            // CSS加载失败时的降级处理
            if (options.type === 'confirm') {
                if (window.confirm(options.content)) {
                    if (typeof options.onConfirm === 'function') {
                        options.onConfirm();
                    }
                } else {
                    if (typeof options.onCancel === 'function') {
                        options.onCancel();
                    }
                }
            } else {
                alert(options.content || '提示');
            }
            return null;
        });
    };

    // 关闭对话框
    const closeDialog = function(id) {
        const instance = popupInstances[id];
        if (!instance) return;
        
        const overlay = instance.element;
        overlay.classList.remove('popup-enter');
        
        setTimeout(() => {
            if (overlay.parentNode) {
                document.body.removeChild(overlay);
            }
            delete popupInstances[id];
        }, defaultConfig.animationDuration);
    };

    // 预加载CSS
    loadCSS();

    // 公共方法
    return {
        /**
         * 显示成功消息
         * @param {string} message 消息内容
         * @param {function} callback 回调函数
         * @param {number} duration 显示时间，毫秒，设为0则不自动关闭
         * @param {object} options 其他选项，如 {showIcon: false} 可以隐藏图标
         */
        success: function(message, callback, duration, options) {
            return createMessage(message, 'success', duration, callback, options);
        },

        /**
         * 显示错误消息
         * @param {string} message 消息内容
         * @param {function} callback 回调函数
         * @param {number} duration 显示时间，毫秒，设为0则不自动关闭
         * @param {object} options 其他选项，如 {showIcon: false} 可以隐藏图标
         */
        error: function(message, callback, duration, options) {
            return createMessage(message, 'error', duration, callback, options);
        },

        /**
         * 显示警告消息
         * @param {string} message 消息内容
         * @param {function} callback 回调函数
         * @param {number} duration 显示时间，毫秒，设为0则不自动关闭
         * @param {object} options 其他选项，如 {showIcon: false} 可以隐藏图标
         */
        warning: function(message, callback, duration, options) {
            return createMessage(message, 'warning', duration, callback, options);
        },

        /**
         * 显示信息消息
         * @param {string} message 消息内容
         * @param {function} callback 回调函数
         * @param {number} duration 显示时间，毫秒，设为0则不自动关闭
         * @param {object} options 其他选项，如 {showIcon: false} 可以隐藏图标
         */
        info: function(message, callback, duration, options) {
            return createMessage(message, 'info', duration, callback, options);
        },

        /**
         * 显示确认对话框
         * @param {string} message 消息内容
         * @param {function} confirmCallback 确认回调
         * @param {function} cancelCallback 取消回调
         * @param {string} title 标题
         * @param {string} cancelText 取消按钮文本
         * @param {string} confirmText 确认按钮文本
         * @param {boolean} showIcon 是否显示图标
         */
        confirm: function(message, confirmCallback, cancelCallback, title, cancelText, confirmText, showIcon) {
            return createDialog({
                type: 'confirm',
                title: title || defaultConfig.confirmTitle,
                content: message,
                onConfirm: confirmCallback,
                onCancel: cancelCallback,
                iconType: 'warning',
                cancelText: cancelText,
                confirmText: confirmText,
                showIcon: showIcon
            });
        },

        /**
         * 显示加载提示
         * @param {string} message 加载提示文字
         * @returns {Promise<string>} 加载层的ID
         */
        loading: function(message) {
            // 如果已经存在加载提示，先关闭
            if (loadingInstance !== null) {
                this.loadingClose();
            }

            const content = `
                <div class="popup-loading">
                    <div class="popup-loading-spinner"></div>
                    <div>${message || defaultConfig.loadingText}</div>
                </div>
            `;
            
            return createDialog({
                content: content,
                showHeader: false,
                showFooter: false,
                width: 'auto'
            }).then(id => {
                loadingInstance = id;
                return id;
            });
        },

        /**
         * 关闭加载提示
         * @param {string} id 加载层的ID，不传则关闭当前加载提示
         */
        loadingClose: function(id) {
            if (id) {
                closeDialog(id);
            } else if (loadingInstance) {
                closeDialog(loadingInstance);
                loadingInstance = null;
            }
        },

        /**
         * 显示自定义内容的弹窗
         * @param {string|DOM|jQuery} content 弹窗内容，可以是HTML字符串、DOM元素或jQuery对象
         * @param {object} options 弹窗选项
         * @returns {Promise<string>} 弹窗的ID
         */
        open: function(content, options) {
            const config = Object.assign({
                type: 'custom',
                title: '提示',
                content: content,
                width: '400px',
                height: 'auto'
            }, options || {});
            
            // 如果content是jQuery对象，获取DOM元素
            if (content && content.jquery) {
                config.content = content[0];
            }
            
            return createDialog(config);
        },

        /**
         * 关闭指定的消息
         * @param {string} id 消息ID
         */
        closeMessage: function(id) {
            closeMessage(id);
        },

        /**
         * 关闭所有弹窗
         */
        closeAll: function() {
            for (const id in popupInstances) {
                closeDialog(id);
            }
            // 清除可能存在的消息提示
            const messages = document.querySelectorAll('.popup-message');
            messages.forEach(msg => {
                if (msg.parentNode) {
                    document.body.removeChild(msg);
                }
            });
        },

        /**
         * 修改配置
         * @param {object} config 要修改的配置项
         */
        config: function(config) {
            if (!config) return;
            Object.assign(defaultConfig, config);
        }
    };
})();

// 导出全局对象
window.customize_pop = customize_pop; 